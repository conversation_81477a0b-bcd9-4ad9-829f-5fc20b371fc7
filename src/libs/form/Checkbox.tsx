import { forwardRef, InputHTMLAttributes, useId } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { mergeClasses } from '@/utils';

const checkboxVariants = cva(
  [
    'rounded border-2 border-gray-300 text-blue-600',
    'focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
    'disabled:cursor-not-allowed disabled:opacity-50',
    'transition-colors duration-200',
  ],
  {
    variants: {
      variant: {
        sm: 'h-4 w-4',
        md: 'h-5 w-5',
        lg: 'h-6 w-6',
      },
    },
    defaultVariants: {
      variant: 'md',
    },
  },
);

const labelVariants = cva('text-gray-700 cursor-pointer', {
  variants: {
    variant: {
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base',
    },
  },
  defaultVariants: {
    variant: 'md',
  },
});

type InputProps = {
  id?: string;
  label?: string;
  error?: string;
  align?: 'left' | 'center' | 'right';
  className?: string;
} & VariantProps<typeof checkboxVariants> &
  InputHTMLAttributes<HTMLInputElement>;

export const Checkbox = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      id,
      label,
      error,
      align = 'left',
      variant = 'md',
      className,
      onChange,
      ...rest
    },
    ref,
  ) => {
    const generatedId = useId();
    const inputId = id || generatedId;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        onChange(e);
      }
    };

    const alignmentClass = {
      left: 'justify-start',
      center: 'justify-center',
      right: 'justify-end',
    }[align];

    return (
      <>
        <div
          className={mergeClasses(
            'relative flex items-center gap-2',
            alignmentClass,
          )}
        >
          <input
            id={inputId}
            ref={ref}
            onChange={handleChange}
            aria-invalid={!!error}
            type="checkbox"
            className={mergeClasses(checkboxVariants({ variant }), className)}
            {...rest}
          />
          {label && (
            <label htmlFor={inputId} className={labelVariants({ variant })}>
              {label}
            </label>
          )}
        </div>
        {error && (
          <p className="mt-1.5 max-w-full text-xs text-red-700">{error}</p>
        )}
      </>
    );
  },
);

Checkbox.displayName = 'Checkbox';
